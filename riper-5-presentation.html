<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riper-5 模式完整培训指南</title>
    
    <!-- RevealJS CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/theme/white.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --accent: #f093fb;
            --success: #4ecdc4;
            --warning: #ffe066;
            --danger: #ff6b6b;
            --dark: #2c3e50;
            --light: #ffffff;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --text-light: #ffffff;
            --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --bg-card: rgba(255, 255, 255, 0.95);
            --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 16px 64px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --border-radius-small: 8px;
        }

        * {
            box-sizing: border-box;
        }

        .reveal {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-light);
            font-size: 18px;
            line-height: 1.6;
        }

        .reveal .slides {
            max-width: 1200px;
            margin: 0 auto;
        }

        .reveal .slides section {
            padding: 1.5rem;
            text-align: left;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            max-height: 90vh;
            overflow-y: auto;
        }

        .reveal h1, .reveal h2, .reveal h3, .reveal h4 {
            font-family: 'Noto Serif SC', -apple-system, BlinkMacSystemFont, serif;
            color: var(--text-light);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            line-height: 1.2;
            margin-bottom: 1rem;
        }

        .reveal h1 {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .reveal h2 {
            font-size: 2rem;
            font-weight: 600;
        }

        .reveal h3 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .reveal h4 {
            font-size: 1.25rem;
            font-weight: 600;
        }

        /* Title Slide */
        .title-slide {
            text-align: center !important;
            background: var(--bg-primary);
            padding: 2rem !important;
            justify-content: center !important;
        }

        .title-slide h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ffffff, #f0f9ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-slide h2 {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .title-slide .author {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 2rem;
        }

        /* Cards */
        .content-card {
            background: var(--bg-card);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            color: var(--text-primary);
            margin: 0.5rem 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        /* Mode Cards */
        .mode-card {
            background: var(--bg-card);
            padding: 1.25rem;
            border-radius: var(--border-radius);
            margin: 0.75rem 0;
            border-left: 4px solid var(--primary);
            box-shadow: var(--shadow-soft);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .mode-card:hover {
            transform: translateX(4px);
            box-shadow: var(--shadow-hover);
        }

        .mode-card h3 {
            color: var(--primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.25rem;
        }

        .mode-icon {
            font-size: 1.25rem !important;
            color: var(--primary) !important;
        }

        /* Highlight Boxes */
        .highlight-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            padding: 1rem;
            border-radius: var(--border-radius-small);
            border-left: 4px solid var(--warning);
            color: #856404;
            margin: 0.75rem 0;
        }

        .warning-box {
            background: linear-gradient(135deg, #f8d7da, #ffb3ba);
            padding: 1rem;
            border-radius: var(--border-radius-small);
            border-left: 4px solid var(--danger);
            color: #721c24;
            margin: 0.75rem 0;
        }

        .success-box {
            background: linear-gradient(135deg, #d1ecf1, #b3e5e0);
            padding: 1rem;
            border-radius: var(--border-radius-small);
            border-left: 4px solid var(--success);
            color: #0c5460;
            margin: 0.75rem 0;
        }

        /* Workflow */
        .workflow-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin: 1.5rem 0;
        }

        .workflow-step {
            background: var(--bg-card);
            padding: 0.75rem 1.25rem;
            border-radius: 50px;
            color: var(--primary);
            font-weight: 600;
            box-shadow: var(--shadow-soft);
            position: relative;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .workflow-step:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-hover);
        }

        .workflow-step::after {
            content: '→';
            position: absolute;
            right: -1.5rem;
            color: var(--text-light);
            font-size: 1.25rem;
        }

        .workflow-step:last-child::after {
            display: none;
        }

        /* Lists */
        .icon-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .icon-list li {
            margin: 0.5rem 0;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            font-size: 0.95rem;
        }

        .icon-list li i {
            color: var(--primary);
            font-size: 1rem;
            width: 1.25rem;
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 1.25rem;
            border-radius: var(--border-radius);
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent);
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            font-size: 0.9rem;
            margin: 0;
            color: var(--text-light);
        }

        /* Code */
        .code-snippet {
            background: #2d3748;
            padding: 1rem;
            border-radius: var(--border-radius-small);
            color: #e2e8f0;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            border: 1px solid #4a5568;
        }

        /* Navigation Hint */
        .navigation-hint {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.8rem;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .reveal {
                font-size: 16px;
            }

            .title-slide h1 {
                font-size: 2.25rem;
            }

            .title-slide h2 {
                font-size: 1.25rem;
            }

            .reveal .slides section {
                padding: 1rem;
                height: auto;
                min-height: 90vh;
            }

            .content-card, .mode-card {
                padding: 1rem;
                margin: 0.5rem 0;
            }

            .workflow-container {
                flex-direction: column;
                gap: 0.5rem;
            }

            .workflow-step::after {
                display: none;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 0.75rem;
            }

            .highlight-box, .warning-box, .success-box {
                padding: 0.75rem;
                margin: 0.5rem 0;
            }

            .icon-list li {
                font-size: 0.9rem;
            }
        }

        @media (max-height: 700px) {
            .reveal .slides section {
                padding: 0.75rem;
                font-size: 0.9rem;
                height: auto;
                min-height: 85vh;
            }

            .reveal h1 {
                font-size: 2rem;
            }

            .reveal h2 {
                font-size: 1.75rem;
            }

            .reveal h3 {
                font-size: 1.35rem;
            }

            .title-slide h1 {
                font-size: 2.5rem;
            }

            .content-card, .mode-card {
                padding: 1rem;
            }

            .highlight-box, .warning-box, .success-box {
                padding: 0.75rem;
            }
        }

        @media (max-height: 600px) {
            .reveal .slides section {
                padding: 0.5rem;
                font-size: 0.85rem;
                min-height: 80vh;
            }

            .reveal h1 {
                font-size: 1.75rem;
            }

            .reveal h2 {
                font-size: 1.5rem;
            }

            .title-slide {
                padding: 1rem !important;
            }

            .title-slide h1 {
                font-size: 2rem;
                margin-bottom: 0.75rem;
            }

            .title-slide h2 {
                font-size: 1.1rem;
                margin-bottom: 1rem;
            }
        }

        /* Custom scrollbar */
        .reveal .slides section::-webkit-scrollbar {
            width: 6px;
        }

        .reveal .slides section::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .reveal .slides section::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .reveal .slides section::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>

<body>
    <div class="reveal">
        <div class="slides">
            <!-- 封面 -->
            <section class="title-slide">
                <h1><i class="fas fa-robot"></i> Riper-5 模式</h1>
                <h2>AI开发工作流程控制系统</h2>
                <div class="author">
                    <p><i class="fas fa-graduation-cap"></i> 完整培训指南</p>
                    <p><i class="fas fa-calendar"></i> 2024年专业版</p>
                </div>
            </section>

            <!-- 目录 -->
            <section>
                <h2><i class="fas fa-list"></i> 课程大纲</h2>
                <div class="content-card">
                    <ul class="icon-list">
                        <li><i class="fas fa-eye"></i> 概述与核心价值</li>
                        <li><i class="fas fa-exclamation-triangle"></i> 问题诊断与背景</li>
                        <li><i class="fas fa-brain"></i> 核心概念与原则</li>
                        <li><i class="fas fa-layer-group"></i> 五大模式详解</li>
                        <li><i class="fas fa-rocket"></i> 实施指南与配置</li>
                        <li><i class="fas fa-star"></i> 最佳实践分享</li>
                        <li><i class="fas fa-lightbulb"></i> 实际应用案例</li>
                        <li><i class="fas fa-tools"></i> 故障排除技巧</li>
                    </ul>
                </div>
            </section>

            <!-- 概述 -->
            <section>
                <h2><i class="fas fa-bullseye"></i> Riper-5 模式概述</h2>
                <div class="content-card">
                    <h3>革命性的AI开发工作流程控制系统</h3>
                    <p>专门设计用于解决Claude 3.7在Cursor IDE中的过度主动性问题</p>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-gem"></i> 核心价值</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-crosshairs"></i> <strong>精确控制</strong> - 防止AI未经授权修改代码</li>
                            <li><i class="fas fa-sitemap"></i> <strong>结构化开发</strong> - 确保流程逻辑性和可预测性</li>
                            <li><i class="fas fa-shield-alt"></i> <strong>质量保证</strong> - 严格的审查机制确保代码质量</li>
                            <li><i class="fas fa-tachometer-alt"></i> <strong>效率提升</strong> - 减少调试时间，提高开发效率</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 背景与问题 -->
            <section>
                <h2><i class="fas fa-exclamation-triangle"></i> Claude 3.7 的核心问题</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-robot"></i></span>
                        <p>过度主动性</p>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-question"></i></span>
                        <p>假设性修改</p>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-memory"></i></span>
                        <p>上下文丢失</p>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-random"></i></span>
                        <p>不可控性</p>
                    </div>
                </div>
                
                <div class="warning-box">
                    <h4><i class="fas fa-bug"></i> 传统开发流程的痛点</h4>
                    <p><strong>开发者提出需求 → AI立即编码 → 自主决定实现 → 引入未预期更改 → 破坏功能 → 大量调试时间</strong></p>
                </div>
            </section>

            <!-- 核心概念 -->
            <section>
                <h2><i class="fas fa-brain"></i> Riper-5 核心概念</h2>
                <div class="content-card">
                    <h3><i class="fas fa-rules"></i> 基本原则</h3>
                    <ul class="icon-list">
                        <li><i class="fas fa-tag"></i> <strong>模式声明强制性</strong> - 每个响应必须以 [MODE: 模式名称] 开头</li>
                        <li><i class="fas fa-lock"></i> <strong>严格权限控制</strong> - 每个模式只能执行特定类型操作</li>
                        <li><i class="fas fa-exchange-alt"></i> <strong>显式转换</strong> - 只有明确指令下才能切换模式</li>
                        <li><i class="fas fa-bullseye"></i> <strong>零偏差执行</strong> - 执行阶段必须100%遵循计划</li>
                        <li><i class="fas fa-check-circle"></i> <strong>强制审查</strong> - 所有实现必须经过严格验证</li>
                    </ul>
                </div>
            </section>

            <!-- 工作流程 -->
            <section>
                <h2><i class="fas fa-flow-chart"></i> Riper-5 工作流程</h2>
                <div class="workflow-container">
                    <div class="workflow-step">
                        <i class="fas fa-search"></i> RESEARCH
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-lightbulb"></i> INNOVATE
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-map"></i> PLAN
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-play"></i> EXECUTE
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-check"></i> REVIEW
                    </div>
                </div>
                <div class="success-box">
                    <p><i class="fas fa-info-circle"></i> <strong>循环迭代</strong> - 从REVIEW模式可以返回INNOVATE或RESEARCH模式，形成持续改进的闭环</p>
                </div>
            </section>

            <!-- 模式1：RESEARCH -->
            <section>
                <div class="mode-card">
                    <h3><i class="fas fa-search mode-icon"></i> 模式1：RESEARCH（研究模式）</h3>
                    <p><strong>目的：</strong>纯信息收集阶段</p>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-check"></i> 允许的操作</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-file-alt"></i> 读取文件内容</li>
                            <li><i class="fas fa-question-circle"></i> 提出澄清问题</li>
                            <li><i class="fas fa-sitemap"></i> 理解代码结构</li>
                            <li><i class="fas fa-analytics"></i> 分析现有功能</li>
                        </ul>
                    </div>
                    
                    <div class="warning-box">
                        <h4><i class="fas fa-ban"></i> 禁止的操作</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-times"></i> 提出建议或解决方案</li>
                            <li><i class="fas fa-times"></i> 实施任何更改</li>
                            <li><i class="fas fa-times"></i> 制定计划</li>
                            <li><i class="fas fa-times"></i> 暗示任何行动</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 模式2：INNOVATE -->
            <section>
                <div class="mode-card">
                    <h3><i class="fas fa-lightbulb mode-icon"></i> 模式2：INNOVATE（创新模式）</h3>
                    <p><strong>目的：</strong>头脑风暴和方案探讨</p>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-check"></i> 允许的操作</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-comments"></i> 讨论可能的解决方案</li>
                            <li><i class="fas fa-balance-scale"></i> 分析优缺点</li>
                            <li><i class="fas fa-comment-dots"></i> 寻求反馈</li>
                            <li><i class="fas fa-route"></i> 探索不同方法</li>
                        </ul>
                    </div>
                    
                    <div class="warning-box">
                        <h4><i class="fas fa-ban"></i> 禁止的操作</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-times"></i> 具体的实现计划</li>
                            <li><i class="fas fa-times"></i> 编写任何代码</li>
                            <li><i class="fas fa-times"></i> 做出最终决定</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 模式3：PLAN -->
            <section>
                <div class="mode-card">
                    <h3><i class="fas fa-map mode-icon"></i> 模式3：PLAN（规划模式）</h3>
                    <p><strong>目的：</strong>创建详尽的技术规范</p>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-check"></i> 允许的操作</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-clipboard-list"></i> 制定详细计划</li>
                            <li><i class="fas fa-folder-open"></i> 指定确切的文件路径</li>
                            <li><i class="fas fa-code"></i> 定义函数名称和更改</li>
                            <li><i class="fas fa-tasks"></i> 创建实施检查清单</li>
                        </ul>
                    </div>
                    
                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 强制要求</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-cog"></i> 计划必须详细到无需创造性决策</li>
                            <li><i class="fas fa-list-ol"></i> 必须转换为编号的顺序检查清单</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 模式4：EXECUTE -->
            <section>
                <div class="mode-card">
                    <h3><i class="fas fa-play mode-icon"></i> 模式4：EXECUTE（执行模式）</h3>
                    <p><strong>目的：</strong>严格按照计划实施</p>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-check"></i> 允许的操作</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-clipboard-check"></i> 仅实施计划中明确详述的内容</li>
                            <li><i class="fas fa-list-ol"></i> 按照检查清单逐项执行</li>
                        </ul>
                    </div>
                    
                    <div class="warning-box">
                        <h4><i class="fas fa-ban"></i> 严格禁止</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-times"></i> 任何偏离计划的行为</li>
                            <li><i class="fas fa-times"></i> 改进或创造性添加</li>
                            <li><i class="fas fa-times"></i> 未在计划中的修改</li>
                        </ul>
                    </div>
                    
                    <div class="success-box">
                        <p><i class="fas fa-info-circle"></i> <strong>偏差处理：</strong>如发现任何需要偏离计划的问题，必须立即返回PLAN模式</p>
                    </div>
                </div>
            </section>

            <!-- 模式5：REVIEW -->
            <section>
                <div class="mode-card">
                    <h3><i class="fas fa-check mode-icon"></i> 模式5：REVIEW（审查模式）</h3>
                    <p><strong>目的：</strong>严格验证实施与计划的一致性</p>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-search"></i> 必须执行</h4>
                        <ul class="icon-list">
                            <li><i class="fas fa-equals"></i> 逐行比较计划与实施</li>
                            <li><i class="fas fa-flag"></i> 标记任何偏差，无论多么微小</li>
                            <li><i class="fas fa-clipboard-check"></i> 报告实施是否与计划完全一致</li>
                        </ul>
                    </div>
                    
                    <div class="code-snippet">
⚠️ 偏差检测：[具体偏差描述]

结论格式：
✅ 实施与计划完全匹配
或
❌ 实施偏离计划
                    </div>
                </div>
            </section>

            <!-- 实施指南 -->
            <section>
                <h2><i class="fas fa-rocket"></i> 实施指南</h2>
                <div class="content-card">
                    <h3><i class="fas fa-cog"></i> 配置方法</h3>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-folder"></i> 方法1：项目规则配置</h4>
                        <ol class="icon-list">
                            <li><i class="fas fa-step-forward"></i> 在Cursor中打开项目设置</li>
                            <li><i class="fas fa-step-forward"></i> 导航到"Rules"部分</li>
                            <li><i class="fas fa-step-forward"></i> 粘贴完整的Riper-5协议</li>
                            <li><i class="fas fa-step-forward"></i> 保存设置</li>
                        </ol>
                    </div>
                    
                    <div class="success-box">
                        <h4><i class="fas fa-terminal"></i> 模式转换指令</h4>
                        <div class="code-snippet">
ENTER RESEARCH MODE
ENTER INNOVATE MODE  
ENTER PLAN MODE
ENTER EXECUTE MODE
ENTER REVIEW MODE
                        </div>
                        <p><strong>重要：</strong>必须使用完全相同的指令格式</p>
                    </div>
                </div>
            </section>

            <!-- 最佳实践 -->
            <section>
                <h2><i class="fas fa-star"></i> 最佳实践</h2>
                <div class="content-card">
                    <ul class="icon-list">
                        <li><i class="fas fa-search"></i> <strong>开始新功能</strong> - 总是从RESEARCH模式开始</li>
                        <li><i class="fas fa-lightbulb"></i> <strong>充分探索</strong> - 在INNOVATE模式中充分讨论所有可能性</li>
                        <li><i class="fas fa-map"></i> <strong>详细规划</strong> - PLAN模式中创建无歧义的实施计划</li>
                        <li><i class="fas fa-play"></i> <strong>严格执行</strong> - EXECUTE模式中不允许任何创造性偏差</li>
                        <li><i class="fas fa-check"></i> <strong>彻底审查</strong> - REVIEW模式中验证每个细节</li>
                    </ul>
                    
                    <div class="highlight-box">
                        <h4><i class="fas fa-building"></i> 项目结构优化</h4>
                        <p>建议采用高度模块化的项目结构，确保每个功能单元都能在200k token限制内处理</p>
                    </div>
                </div>
            </section>

            <!-- 应用案例 -->
            <section>
                <h2><i class="fas fa-lightbulb"></i> 实际应用案例</h2>
                <div class="content-card">
                    <div class="mode-card">
                        <h3><i class="fas fa-globe"></i> 案例：Web应用功能开发</h3>
                        <p><strong>场景：</strong>为电商网站添加用户评论功能</p>
                        
                        <div class="workflow-container">
                            <div class="workflow-step">
                                <small>RESEARCH</small><br>
                                分析现有架构
                            </div>
                            <div class="workflow-step">
                                <small>INNOVATE</small><br>
                                设计方案
                            </div>
                            <div class="workflow-step">
                                <small>PLAN</small><br>
                                详细规划
                            </div>
                            <div class="workflow-step">
                                <small>EXECUTE</small><br>
                                严格实施
                            </div>
                            <div class="workflow-step">
                                <small>REVIEW</small><br>
                                验证结果
                            </div>
                        </div>
                        
                        <div class="success-box">
                            <p><i class="fas fa-trophy"></i> <strong>结果：</strong>零偏差实施，代码质量显著提升，调试时间减少80%</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 故障排除 -->
            <section>
                <h2><i class="fas fa-tools"></i> 故障排除</h2>
                <div class="content-card">
                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 常见问题及解决方案</h4>
                        
                        <h5><i class="fas fa-robot"></i> AI忽略模式声明</h5>
                        <ul class="icon-list">
                            <li><i class="fas fa-info"></i> <strong>原因：</strong>协议未正确加载或上下文过载</li>
                            <li><i class="fas fa-wrench"></i> <strong>解决：</strong>重新粘贴完整协议，简化工作范围</li>
                        </ul>
                        
                        <h5><i class="fas fa-ban"></i> 模式权限违规</h5>
                        <ul class="icon-list">
                            <li><i class="fas fa-info"></i> <strong>原因：</strong>AI在错误模式下执行不当操作</li>
                            <li><i class="fas fa-wrench"></i> <strong>解决：</strong>立即停止，明确指出违规，重申限制</li>
                        </ul>
                        
                        <h5><i class="fas fa-memory"></i> 上下文丢失</h5>
                        <ul class="icon-list">
                            <li><i class="fas fa-shield-alt"></i> <strong>预防：</strong>定期总结进度，使用更小的工作单元</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 进阶技巧 -->
            <section>
                <h2><i class="fas fa-graduation-cap"></i> 进阶技巧</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-layer-group"></i></span>
                        <p>嵌套模式系统</p>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-adjust"></i></span>
                        <p>动态协议调整</p>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-users"></i></span>
                        <p>团队协作增强</p>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number"><i class="fas fa-shield-alt"></i></span>
                        <p>质量保证集成</p>
                    </div>
                </div>
                
                <div class="highlight-box">
                    <h4><i class="fas fa-chart-line"></i> 持续改进策略</h4>
                    <ol class="icon-list">
                        <li><i class="fas fa-chart-bar"></i> 收集使用数据：记录模式切换频率、违规次数</li>
                        <li><i class="fas fa-analytics"></i> 分析效果：对比使用前后的开发效率</li>
                        <li><i class="fas fa-comments"></i> 团队反馈：收集开发者使用体验</li>
                        <li><i class="fas fa-sync"></i> 协议迭代：基于反馈优化协议内容</li>
                    </ol>
                </div>
            </section>

            <!-- 总结 -->
            <section class="title-slide">
                <h1><i class="fas fa-flag-checkered"></i> 总结</h1>
                <div class="content-card">
                    <h3>Riper-5模式的价值实现</h3>
                    <ul class="icon-list">
                        <li><i class="fas fa-shield-alt"></i> <strong>严格遵循协议</strong> - 确保每个步骤按规定执行</li>
                        <li><i class="fas fa-adjust"></i> <strong>适应性调整</strong> - 根据项目特点调整模式参数</li>
                        <li><i class="fas fa-sync"></i> <strong>持续优化</strong> - 基于使用经验不断改进工作流程</li>
                        <li><i class="fas fa-users"></i> <strong>团队协作</strong> - 确保团队成员理解遵循相同协议</li>
                    </ul>
                    
                    <div class="success-box">
                        <h4><i class="fas fa-trophy"></i> 最终目标</h4>
                        <p><strong>将AI从"野兽"驯化为"良驹"，实现真正高效、可控的AI辅助开发体验</strong></p>
                    </div>
                </div>
            </section>

            <!-- 感谢页面 -->
            <section class="title-slide">
                <h1><i class="fas fa-heart"></i> 感谢聆听</h1>
                <h2>开始您的Riper-5模式之旅</h2>
                <div class="author">
                    <p><i class="fas fa-envelope"></i> 欢迎交流与反馈</p>
                    <p><i class="fas fa-rocket"></i> 让AI开发更加高效可控</p>
                </div>
            </section>
        </div>
    </div>

    <!-- Navigation hint -->
    <div class="navigation-hint">
        <i class="fas fa-hand-pointer"></i> 使用方向键或空格键导航
    </div>

    <!-- RevealJS Script -->
    <script src="https://cdn.jsdelivr.net/npm/reveal.js@4.3.1/dist/reveal.js"></script>
    
    <script>
        Reveal.initialize({
            hash: true,
            center: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            keyboard: true,
            overview: true,
            loop: false,
            rtl: false,
            shuffle: false,
            fragments: true,
            fragmentInURL: true,
            embedded: false,
            help: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoSlide: 0,
            autoSlideStoppable: true,
            autoSlideMethod: 'next',
            defaultTiming: null,
            mouseWheel: true,
            hideInactiveCursor: true,
            hideCursorTime: 5000,
            previewLinks: false,
            focusBodyOnPageVisibilityChange: true,
            
            // Display settings
            display: 'block',
            
            // Touch settings
            touch: true,
            
            // View distance
            viewDistance: 3,
            
            // Mobile optimizations
            mobileViewDistance: 2
        });

        // Auto-hide navigation hint after 5 seconds
        setTimeout(() => {
            const hint = document.querySelector('.navigation-hint');
            if (hint) {
                hint.style.opacity = '0';
                hint.style.transition = 'opacity 1s ease';
                setTimeout(() => hint.remove(), 1000);
            }
        }, 5000);

        // Add slide change animation
        Reveal.on('slidechanged', event => {
            // Add fade-in animation to content cards
            const cards = event.currentSlide.querySelectorAll('.content-card, .mode-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>